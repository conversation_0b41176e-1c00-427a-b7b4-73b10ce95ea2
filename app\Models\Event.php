<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'latitude',
        'longitude',
        'address',
        'allowed_radius',
        'start_time',
        'end_time',
        'active',
        'user_id',
        'qr_code',
    ];

     protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            $event->qr_code = Str::random(32);
        });
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }

    public function attendees()
    {
        return $this->belongsToMany(User::class, 'attendances')
                    ->withPivot('checked_in_at', 'verified', 'distance_meters');
    }

    public function isActive()
    {
        $now = now();
        return $this->active &&
               $now->gte($this->start_time) &&
               $now->lte($this->end_time);
    }
}
